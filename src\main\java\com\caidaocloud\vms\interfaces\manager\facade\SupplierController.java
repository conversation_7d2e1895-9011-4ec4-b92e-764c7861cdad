package com.caidaocloud.vms.interfaces.manager.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.vms.application.dto.SupplierContactDto;
import com.caidaocloud.vms.application.dto.SupplierContractDto;
import com.caidaocloud.vms.application.dto.SupplierDto;
import com.caidaocloud.vms.application.dto.SupplierQueryDTO;
import com.caidaocloud.vms.application.dto.SupplierTaxInfoDto;
import com.caidaocloud.vms.application.service.SupplierService;
import com.caidaocloud.vms.application.vo.SupplierContactVO;
import com.caidaocloud.vms.application.vo.SupplierContractVO;
import com.caidaocloud.vms.application.vo.SupplierDetailVO;
import com.caidaocloud.vms.application.vo.SupplierPageVO;
import com.caidaocloud.vms.application.vo.SupplierSelectVO;
import com.caidaocloud.vms.application.vo.SupplierTaxInfoVO;
import com.caidaocloud.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 供应商管理控制器
 * 
 * <AUTHOR> Zhou
 * @date 2025/5/27
 */
@RestController
@RequestMapping("/api/vms/v1/manager/supplier")
@Api(tags = "供应商管理", description = "供应商信息的增删改查接口")
public class SupplierController {

        @Autowired
        private SupplierService supplierService;

        /**
         * 分页查询供应商列表
         * 
         * @param queryDTO 查询条件
         * @return 供应商列表
         */
        @PostMapping("/page")
        @ApiOperation(value = "分页查询供应商列表", notes = "根据查询条件获取供应商分页列表")
        public Result<PageResult<SupplierPageVO>> supplierPage(
                        @ApiParam(value = "查询条件", required = true) @RequestBody SupplierQueryDTO queryDTO) {
                PageResult<SupplierPageVO> result = supplierService.supplierPage(queryDTO);
                return Result.ok(result);
        }

        @GetMapping("/selectList")
        @ApiOperation(value = "供应商下拉列表")
        public Result<List<SupplierSelectVO>> supplierList() {
                return Result.ok(supplierService.supplierList());
        }

        /**
         * 保存供应商
         * 
         * @param supplierDto 供应商信息
         * @return 操作结果
         */
        @PostMapping("/save")
        @ApiOperation(value = "新增供应商", notes = "创建新的供应商信息")
        public Result saveSupplier(@ApiParam(value = "供应商信息", required = true) @RequestBody SupplierDto supplierDto) {
                supplierService.saveSupplier(supplierDto);
                return Result.ok();

        }

        /**
         * 编辑供应商
         * 
         * @param supplierDto 供应商信息
         * @return 操作结果
         */
        @PostMapping("/edit")
        @ApiOperation(value = "编辑供应商", notes = "更新已有供应商的基本信息")
        public Result editSupplier(@ApiParam(value = "供应商信息", required = true) @RequestBody SupplierDto supplierDto) {

                supplierService.editSupplier(supplierDto);
                return Result.ok();

        }

        /**
         * 根据ID加载供应商基础信息
         * 
         * @param supplierId 供应商ID
         * @return 供应商详细信息
         */
        @GetMapping("/detail")
        @ApiOperation(value = "获取供应商详情", notes = "根据ID获取供应商的详细信息")
        public Result<SupplierDetailVO> loadSupplier(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                SupplierDetailVO vo = supplierService.loadSupplier(supplierId);
                return Result.ok(vo);

        }

        /**
         * 根据ID加载供应商基础信息并赋值txt文本
         *
         * @param supplierId 供应商ID
         * @return 供应商详细信息
         */
        @GetMapping("/detail/txt")
        @ApiOperation(value = "获取供应商详情(含txt文本)", notes = "根据ID获取供应商的详细信息，包含相关字段的txt文本")
        public Result<SupplierDetailVO> loadSupplierTxt(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                SupplierDetailVO vo = supplierService.loadSupplierTxt(supplierId);
                return Result.ok(vo);

        }

        /**
         * 将供应商状态变更为终止
         * 
         * @return 操作结果
         */
        @PostMapping("/terminate")
        @ApiOperation(value = "终止供应商", notes = "将供应商状态变更为终止")
        public Result terminateSupplier(@ApiParam(value = "供应商ID", required = true) @RequestBody SupplierDto supplier) {

                supplierService.terminated(supplier.getBid());
                return Result.ok();

        }

        /**
         * 将供应商状态变更为终止
         *
         * @return 操作结果
         */
        @PostMapping("/active")
        @ApiOperation(value = "终止供应商", notes = "将供应商状态变更为终止")
        public Result activeSupplier(@ApiParam(value = "供应商ID", required = true) @RequestBody SupplierDto supplier) {

                supplierService.active(supplier.getBid());
                return Result.ok();

        }

        /**
         * 将供应商状态变更为终止
         *
         * @return 操作结果
         */
        @PostMapping("/delete")
        @ApiOperation(value = "删除供应商")
        public Result deleteSupplier(@ApiParam(value = "供应商ID", required = true) @RequestBody SupplierDto supplier) {

                supplierService.delete(supplier.getBid());
                return Result.ok();

        }

        /**
         * 根据ID加载供应商税务信息
         * 
         * @param supplierId 供应商ID
         * @return 供应商税务信息
         */
        @GetMapping("/tax")
        @ApiOperation(value = "获取供应商税务信息", notes = "根据ID获取供应商的税务信息")
        public Result<SupplierTaxInfoVO> loadSupplierTaxInfo(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                SupplierTaxInfoVO vo = supplierService.loadSupplierTaxInfo(supplierId);
                return Result.ok(vo);

        }

        /**
         * 编辑供应商税务信息
         * 
         * @param taxInfoDto 税务信息
         * @return 操作结果
         */
        @PostMapping("/tax/edit")
        @ApiOperation(value = "编辑供应商税务信息", notes = "更新供应商的税务相关信息")
        public Result editTaxInfo(
                        @ApiParam(value = "税务信息", required = true) @RequestBody SupplierTaxInfoDto taxInfoDto) {

                supplierService.editTaxInfo(taxInfoDto);
                return Result.ok();

        }

        /**
         * 保存供应商联系人
         * 
         * @param contactDto 联系人信息
         * @return 操作结果
         */
        @PostMapping("/contact/save")
        @ApiOperation(value = "新增供应商联系人", notes = "为供应商添加新的联系人")
        public Result saveContact(
                        @ApiParam(value = "联系人信息", required = true) @RequestBody SupplierContactDto contactDto) {

                supplierService.saveContact(contactDto);
                return Result.ok();

        }

        /**
         * 编辑供应商联系人
         * 
         * @param contactDto 联系人信息
         * @return 操作结果
         */
        @PostMapping("/contact/edit")
        @ApiOperation(value = "编辑供应商联系人", notes = "更新供应商的联系人信息")
        public Result editContact(
                        @ApiParam(value = "联系人信息", required = true) @RequestBody SupplierContactDto contactDto) {

                supplierService.editContact(contactDto);
                return Result.ok();

        }

        /**
         * 获取供应商所有联系人信息
         * 
         * @param supplierId 供应商ID
         * @return 联系人列表
         */
        @GetMapping("/contact/list")
        @ApiOperation(value = "获取供应商联系人列表", notes = "获取指定供应商的所有联系人信息")
        public Result<List<SupplierContactVO>> supplierContactList(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                List<SupplierContactVO> list = supplierService.supplierContactList(supplierId);
                return Result.ok(list);

        }

        /**
         * 获取供应商所有联系人信息
         *
         * @param supplierId 供应商ID
         * @return 联系人列表
         */
        @GetMapping("/contact/selectList")
        @ApiOperation(value = "获取供应商联系人下拉列表", notes = "获取指定供应商的启用中联系人信息")
        public Result<List<SupplierContactVO>> supplierContactSelectList(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                List<SupplierContactVO> list = supplierService.supplierContactSelectList(supplierId);
                return Result.ok(list);

        }

        /**
         * 保存供应商合同
         * 
         * @param contractDto 合同信息
         * @return 操作结果
         */
        @PostMapping("/contract/save")
        @ApiOperation(value = "新增供应商合同", notes = "为供应商添加新的合同")
        public Result saveContract(
                        @ApiParam(value = "合同信息", required = true) @RequestBody SupplierContractDto contractDto) {

                supplierService.saveContract(contractDto);
                return Result.ok();

        }

        /**
         * 编辑供应商合同
         * 
         * @param contractDto 合同信息
         * @return 操作结果
         */
        @PostMapping("/contract/edit")
        @ApiOperation(value = "编辑供应商合同", notes = "更新供应商的合同信息")
        public Result editContract(
                        @ApiParam(value = "合同信息", required = true) @RequestBody SupplierContractDto contractDto) {

                supplierService.editContract(contractDto);
                return Result.ok();

        }

        /**
         * 获取供应商所有合同信息
         * 
         * @param supplierId 供应商ID
         * @return 合同列表
         */
        @GetMapping("/contract/list")
        @ApiOperation(value = "获取供应商合同列表", notes = "获取指定供应商的所有合同信息")
        public Result<List<SupplierContractVO>> supplierContractList(
                        @ApiParam(value = "供应商ID", required = true) @RequestParam String supplierId) {

                List<SupplierContractVO> list = supplierService.supplierContractList(supplierId);
                return Result.ok(list);

        }

        /**
         * 启用供应商联系人
         * 
         * @param contactId 联系人ID
         * @return 操作结果
         */
        @PostMapping("/contact/activate")
        @ApiOperation(value = "启用供应商联系人", notes = "将供应商联系人状态变更为启用")
        public Result activateContact(
                        @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

                supplierService.activateContact(contactId);
                return Result.ok();

        }

        /**
         * 停用供应商联系人
         *
         * @param contactId 联系人ID
         * @return 操作结果
         */
        @PostMapping("/contact/deactivate")
        @ApiOperation(value = "停用供应商联系人", notes = "将供应商联系人状态变更为停用")
        public Result deactivateContact(
                        @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

                supplierService.deactivateContact(contactId);
                return Result.ok();

        }

        /**
         * 设置主要联系人
         *
         * @param contactId 联系人ID
         * @return 操作结果
         */
        @PostMapping("/contact/setPrimary")
        @ApiOperation(value = "设置主要联系人", notes = "将指定联系人设置为主要联系人，一个供应商只有一个主要联系人")
        public Result setPrimaryContact(
                        @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

                supplierService.enableContactPrimary(contactId);
                return Result.ok();

        }

        /**
         * 取消主要联系人
         *
         * @param contactId 联系人ID
         * @return 操作结果
         */
        @PostMapping("/contact/unsetPrimary")
        @ApiOperation(value = "取消主要联系人", notes = "取消指定联系人的主要联系人状态")
        public Result unsetPrimaryContact(
                        @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

                supplierService.disableContactPrimary(contactId);
                return Result.ok();

        }

        /**
         * 删除供应商联系人
         * 
         * @param contactId 联系人ID
         * @return 操作结果
         */
        @PostMapping("/contact/delete")
        @ApiOperation(value = "删除供应商联系人", notes = "删除指定的供应商联系人")
        public Result deleteContact(
                        @ApiParam(value = "联系人ID", required = true) @RequestParam String contactId) {

                supplierService.deleteContact(contactId);
                return Result.ok();

        }

        /**
         * 删除供应商合同
         * 
         * @param contractId 合同ID
         * @return 操作结果
         */
        @PostMapping("/contract/delete")
        @ApiOperation(value = "删除供应商合同", notes = "删除指定的供应商合同")
        public Result deleteContract(
                        @ApiParam(value = "合同ID", required = true) @RequestParam String contractId) {

                supplierService.deleteContract(contractId);
                return Result.ok();

        }
}
