package com.caidaocloud.vms.application.service.ats;

import java.util.ArrayList;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.RuleDto;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.googlecode.totallylazy.Pair;
import com.ruipin.metadata.common.component.ComponentTypeEnum;
import com.ruipin.metadata.domain.vo.EntityDefVo;
import com.ruipin.metadata.domain.vo.EntityPropertyDefVo;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2026/1/12
 */
@Service
public class AtsModuleService {

	public void convert2PaasModule(EntityDefVo atsDef){

	}

	public MetadataPropertyDto convert(EntityPropertyDefVo def) {
		MetadataPropertyDto dto = new MetadataPropertyDto();
		dto.setName(def.getPropertyName());
		// TODO: 2026/1/12 字段名ats没有
		// dto.setProperty(def.getPropertyEngName());

		Pair<PropertyDataType, String> paasType = convert2PaasType(def.getInputType());
		dto.setDataType(paasType.first());
		dto.setWidgetType(paasType.second());
		dto.setRules(new ArrayList<>());
		appendPropertyRule(dto, def);
		return dto;
	}

	private void appendPropertyRule(MetadataPropertyDto dto, EntityPropertyDefVo def) {
		if ("textArea".equals(dto.getWidgetType())) {
			RuleDto rule = new RuleDto();
			rule.setType("maxLength");
			rule.setValue("2000");
		}

	}


	private Pair<PropertyDataType,String> convert2PaasType(String inputType){
		try {
			ComponentTypeEnum typeEnum = ComponentTypeEnum.valueOf(inputType);
			PropertyDataType a=null;
			String b=null;
			switch (typeEnum) {
			case input:
				a=  PropertyDataType.String;
				b = "text";
				break;
			case txt_area:
				a=  PropertyDataType.String;
				b = "textArea";
				break;
			case check:
				a= PropertyDataType.Dict;
				b = "EnumSelect";
			break;
			case select:
				a= PropertyDataType.Enum;
				b="enum";
			break;
			case date_year_month:
			case date_year_month_day:
				a= PropertyDataType.Timestamp;
				b = "date";
			break;
			case input_number:
				a= PropertyDataType.Number;
				b = "float";
			break;
			default:
				throw new ServerException("不支持的ats字段类型" + inputType);
			}
			return Pair.pair(a, b);
		}catch (Exception e){
			throw new ServerException("ats字段类型" + inputType + "不存在");
		}
	}
}
